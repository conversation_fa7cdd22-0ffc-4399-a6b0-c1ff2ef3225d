{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "'pending_merchant'", "enumItems": ["pending_merchant", "pending_customer", "accepted", "customer_rejected", "merchant_rejected"], "mappedType": "enum"}, "customer_id": {"name": "customer_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "draft_order_id": {"name": "draft_order_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "order_change_id": {"name": "order_change_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "cart_id": {"name": "cart_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "quote", "schema": "public", "indexes": [{"keyName": "IDX_quote_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_quote_deleted_at\" ON \"quote\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "quote_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "text": {"name": "text", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "item_id": {"name": "item_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "admin_id": {"name": "admin_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "customer_id": {"name": "customer_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "quote_id": {"name": "quote_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "message", "schema": "public", "indexes": [{"keyName": "IDX_message_quote_id", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_message_quote_id\" ON \"message\" (quote_id) WHERE deleted_at IS NULL"}, {"keyName": "IDX_message_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_message_deleted_at\" ON \"message\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "message_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {"message_quote_id_foreign": {"constraintName": "message_quote_id_foreign", "columnNames": ["quote_id"], "localTableName": "public.message", "referencedColumnNames": ["id"], "referencedTableName": "public.quote", "updateRule": "cascade"}}}]}