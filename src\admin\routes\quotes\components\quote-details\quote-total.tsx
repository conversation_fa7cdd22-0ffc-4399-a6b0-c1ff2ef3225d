import { AdminOrder, AdminOrderPreview } from "@medusajs/framework/types";
import { Text } from "@medusajs/ui";
import { formatAmount } from "../../../../utils";
import { useB2BTranslation } from "../../../../hooks/use-b2b-translation";

export const QuoteTotal = ({
  order,
  preview,
}: {
  order: AdminOrder;
  preview: AdminOrderPreview;
}) => {
  const { t } = useB2BTranslation();

  return (
    <div className=" flex flex-col gap-y-2 px-6 py-4">
      <div className="text-ui-fg-base flex items-center justify-between">
        <Text
          weight="plus"
          className="text-ui-fg-subtle"
          size="small"
          leading="compact"
        >
          {t("routes.quotes.details.originalTotal")}
        </Text>
        <Text
          weight="plus"
          className="text-ui-fg-subtle"
          size="small"
          leading="compact"
        >
          {formatAmount(order.total, order.currency_code)}
        </Text>
      </div>

      <div className="text-ui-fg-base flex items-center justify-between">
        <Text
          className="text-ui-fg-subtle text-semibold"
          size="small"
          leading="compact"
          weight="plus"
        >
          {t("routes.quotes.details.quoteTotal")}
        </Text>
        <Text
          className="text-ui-fg-subtle text-bold"
          size="small"
          leading="compact"
          weight="plus"
        >
          {formatAmount(preview.summary.current_order_total, order.currency_code)}
        </Text>
      </div>
    </div>
  );
};
