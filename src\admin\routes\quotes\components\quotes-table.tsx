import { DataTable } from "../../../../admin/components";
import { useDataTable } from "../../../../admin/hooks";
import { useQuotes } from "../../../../admin/hooks/api";
import { useB2BTranslation } from "../../../hooks/use-b2b-translation";
import { useQuotesTableColumns } from "./table/columns";
import { useQuotesTableFilters } from "./table/filters";
import { useQuotesTableQuery } from "./table/query";

const PAGE_SIZE = 50;
const PREFIX = "quo";

export const QuotesTable = () => {
  const { t } = useB2BTranslation();
  const { searchParams, raw } = useQuotesTableQuery({
    pageSize: PAGE_SIZE,
    prefix: PREFIX,
  });

  const {
    quotes = [],
    count,
    isPending,
  } = useQuotes({
    ...searchParams,
    fields:
      "+draft_order.total,+draft_order.customer.email,*draft_order.customer.employee.company",
    order: "-created_at",
  });

  const columns = useQuotesTableColumns();
  const filters = useQuotesTableFilters();

  const { table } = useDataTable({
    data: quotes,
    columns,
    enablePagination: true,
    count,
    pageSize: PAGE_SIZE,
  });

  return (
    <div className="flex size-full flex-col overflow-hidden">
      <DataTable
        columns={columns}
        table={table}
        pagination
        navigateTo={(row) => `/quotes/${row.original.id}`}
        filters={filters}
        count={count}
        search
        isLoading={isPending}
        pageSize={PAGE_SIZE}
        orderBy={["id", "created_at"]}
        queryObject={raw}
        noRecords={{
          title: t("routes.quotes.noQuotes"),
          message: t("routes.quotes.noQuotesDescription"),
        }}
      />
    </div>
  );
};
