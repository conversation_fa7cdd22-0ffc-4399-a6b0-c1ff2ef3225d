{"namespaces": ["public"], "name": "public", "tables": [{"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "cart_id": {"name": "cart_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "type": {"name": "type", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["admin", "sales_manager"], "mappedType": "enum"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["pending", "approved", "rejected"], "mappedType": "enum"}, "created_by": {"name": "created_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "handled_by": {"name": "handled_by", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "mappedType": "text"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "approval", "schema": "public", "indexes": [{"keyName": "IDX_approval_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_approval_deleted_at\" ON \"approval\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "approval_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "company_id": {"name": "company_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "requires_admin_approval": {"name": "requires_admin_approval", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "requires_sales_manager_approval": {"name": "requires_sales_manager_approval", "type": "boolean", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "default": "false", "mappedType": "boolean"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "approval_settings", "schema": "public", "indexes": [{"keyName": "IDX_approval_settings_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_approval_settings_deleted_at\" ON \"approval_settings\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "approval_settings_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}, {"columns": {"id": {"name": "id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "cart_id": {"name": "cart_id", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "mappedType": "text"}, "status": {"name": "status", "type": "text", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "enumItems": ["pending", "approved", "rejected"], "mappedType": "enum"}, "created_at": {"name": "created_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "updated_at": {"name": "updated_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": false, "length": 6, "default": "now()", "mappedType": "datetime"}, "deleted_at": {"name": "deleted_at", "type": "timestamptz", "unsigned": false, "autoincrement": false, "primary": false, "nullable": true, "length": 6, "mappedType": "datetime"}}, "name": "approval_status", "schema": "public", "indexes": [{"keyName": "IDX_approval_status_deleted_at", "columnNames": [], "composite": false, "primary": false, "unique": false, "expression": "CREATE INDEX IF NOT EXISTS \"IDX_approval_status_deleted_at\" ON \"approval_status\" (deleted_at) WHERE deleted_at IS NULL"}, {"keyName": "approval_status_pkey", "columnNames": ["id"], "composite": false, "primary": true, "unique": true}], "checks": [], "foreignKeys": {}}]}