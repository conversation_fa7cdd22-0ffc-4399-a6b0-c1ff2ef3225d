{"b2b": {"nav": {"quotes": "Quotes", "companies": "Companies", "approvals": "Approvals"}, "routes": {"quotes": {"title": "Quotes", "subtitle": "Manage customer quotes and proposals", "noQuotes": "No quotes found", "noQuotesDescription": "There are currently no quotes. Create one from the storefront.", "table": {"id": "Quote ID", "customer": "Customer", "status": "Status", "company": "Company", "amount": "Amount", "createdAt": "Created", "actions": "Actions"}, "status": {"pending_merchant": "Pending Merchant", "pending_customer": "Pending Customer", "merchant_rejected": "Merchant Rejected", "customer_rejected": "Customer Rejected", "accepted": "Accepted"}, "actions": {"sendQuote": "Send Quote", "rejectQuote": "Reject Quote", "viewOrder": "View Order"}, "details": {"header": "Quote Det<PERSON>", "quoteSummary": "Quote Summary", "customer": "Customer", "items": "Items", "total": "Total", "subtotal": "Subtotal", "shipping": "Shipping", "tax": "Tax", "messages": "Messages", "originalTotal": "Original Total", "quoteTotal": "Quote Total", "discounts": "Discounts", "pickQuoteItem": "Pick Quote <PERSON>em", "selectQuoteItem": "Select a quote item to write a message around", "selectItem": "Select Item", "send": "Send", "manage": "Manage", "manageQuote": "Manage Quote", "updatePrice": "Update Price", "overridePriceHint": "Override the unit price of this product", "phone": "Phone", "spendingLimit": "Spending Limit", "company": "Company", "name": "Name"}, "confirmations": {"sendTitle": "Send quote?", "sendDescription": "You are about to send this quote to the customer. Do you want to continue?", "rejectTitle": "Reject quote?", "rejectDescription": "You are about to reject this customer's quote. Do you want to continue?"}, "toasts": {"sendSuccess": "Successfully sent quote to customer", "rejectSuccess": "Successfully rejected customer's quote", "messageSuccess": "Successfully sent message to customer", "updateSuccess": "Successfully updated quote"}, "acceptance": {"message": "Quote accepted by customer. Order is ready for processing."}}, "companies": {"title": "Companies", "subtitle": "Manage customer companies and organizations", "createCompany": "Create Company", "noCompanies": "No companies found", "noCompaniesDescription": "There are currently no companies. Create one to get started.", "table": {"name": "Name", "phone": "Phone", "email": "Email", "address": "Address", "employees": "Employees", "customerGroup": "Customer Group", "actions": "Actions"}, "form": {"generalInfo": "General Information", "name": "Company Name", "email": "Email", "phone": "Phone Number", "address": "Address", "city": "City", "state": "State", "zipCode": "ZIP Code", "logo": "Company Logo", "website": "Website", "description": "Description"}, "actions": {"edit": "Edit Company", "delete": "Delete Company", "viewEmployees": "View Employees", "assignGroup": "Assign Customer Group", "editDetails": "Edit details", "manageCustomerGroup": "Manage customer group", "approvalSettings": "Approval settings"}, "details": {"overview": "Overview", "employees": "Employees", "orders": "Orders", "settings": "Settings"}, "toasts": {"updateSuccess": "Company updated successfully", "updateError": "Failed to update company", "deleteSuccess": "Company deleted successfully", "addToGroupSuccess": "Company added to customer group successfully", "addToGroupError": "Failed to add company to customer group", "removeFromGroupSuccess": "Company removed from customer group successfully", "removeFromGroupError": "Failed to remove company from customer group"}, "customerGroup": {"title": "Manage Customer Group", "addToGroup": "Add to Customer Group", "hint": "Adding {companyName} to a customer group will automatically add {employeeCount} linked employee{employeeCount, plural, one {} other {s}} to the customer group.", "customerGroupColumn": "Customer Group", "actionsColumn": "Actions", "addButton": "Add", "removeButton": "Remove", "noGroupsFound": "No customer groups found"}, "approvalSettings": {"title": "Company Approval Settings", "requiresAdminApproval": "Requires <PERSON><PERSON>", "requiresAdminApprovalDesc": "Require company admin approval for all orders placed by this company.", "requiresSalesManagerApproval": "Requires Sales Manager <PERSON><PERSON><PERSON><PERSON>", "requiresSalesManagerApprovalDesc": "Require sales manager approval for all orders placed by this company.", "updateSuccess": "Company approval settings updated successfully", "updateError": "Failed to update company approval settings"}, "companyDetails": {"notFound": "Company not found", "phone": "Phone", "email": "Email", "address": "Address", "city": "City", "state": "State", "currency": "<PERSON><PERSON><PERSON><PERSON>", "customerGroup": "Customer Group", "approvalSettings": "Approval Settings", "requiresAdminApproval": "Requires admin approval", "requiresSalesManagerApproval": "Requires sales manager approval", "noApprovalRequired": "No approval required", "employees": "Employees", "name": "Name", "spendingLimit": "Spending Limit", "actions": "Actions", "admin": "Admin", "add": "Add", "noEmployees": "No records", "noEmployeesDesc": "This company doesn't have any employees."}, "employees": {"createEmployee": "Add Company Customer", "createEmployeeSuccess": "Employee created successfully", "createCustomerError": "Failed to create customer", "createEmployeeError": "Failed to create employee", "form": {"details": "Details", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "permissions": "Permissions", "spendingLimit": "Spending Limit", "adminAccess": "Admin Access", "isAdmin": "Is Admin", "isAdminDesc": "Enable to grant admin access", "isAdminTooltip": "Admins can manage the company's details and employee permissions.", "firstNamePlaceholder": "<PERSON>", "lastNamePlaceholder": "<PERSON><PERSON>", "emailPlaceholder": "<EMAIL>", "phonePlaceholder": "0612345678", "spendingLimitPlaceholder": "1000", "saving": "Saving...", "save": "Save"}, "actions": {"edit": "Edit", "delete": "Delete", "deleteSuccess": "Employee deleted successfully"}, "edit": {"title": "Edit Employee", "updateSuccess": "Employee updated successfully", "editCustomerDetails": "Edit Customer Details", "company": "Company"}}}, "approvals": {"title": "Approvals", "subtitle": "Manage order approval workflows", "noApprovals": "No approvals found", "noApprovalsDescription": "There are currently no pending approvals.", "table": {"orderId": "Order ID", "customer": "Customer", "company": "Company", "amount": "Amount", "status": "Status", "requestedAt": "Requested", "actions": "Actions"}, "status": {"pending": "Pending Approval", "approved": "Approved", "rejected": "Rejected", "expired": "Expired"}, "actions": {"approve": "Approve", "reject": "Reject", "review": "Review"}, "details": {"approvalRequest": "Approval Request", "requestedBy": "Requested By", "reason": "Reason", "orderDetails": "Order Details"}, "confirmations": {"approveTitle": "Approve order?", "approveDescription": "You are about to approve this order. Do you want to continue?", "rejectTitle": "Reject order?", "rejectDescription": "You are about to reject this order approval request. Do you want to continue?"}, "toasts": {"approveSuccess": "Order approved successfully", "rejectSuccess": "Order approval rejected successfully"}}}, "common": {"loading": "Loading...", "noData": "No data available", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "create": "Create", "back": "Back", "next": "Next", "previous": "Previous", "confirmDeletion": "Confirm Deletion", "confirmDeletionDesc": "Are you sure you want to delete this item? This action cannot be undone.", "confirmLeaveForm": "Are you sure you want to leave this form?", "confirmLeaveFormDesc": "You have unsaved changes that will be lost if you exit this form.", "continue": "Continue"}}}