{"b2b": {"nav": {"quotes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "companies": "<PERSON><PERSON><PERSON>lat<PERSON>", "approvals": "Jóváhagyások"}, "routes": {"quotes": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subtitle": "Ügyféli ajánlatok és javaslatok kezelése", "noQuotes": "<PERSON><PERSON><PERSON> rekord", "noQuotesDescription": "Jelenleg nincsenek ajánlatok. Hozzon létre egyet az üzletből.", "table": {"id": "Aj<PERSON><PERSON>", "customer": "Ügyfél", "status": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Összeg", "createdAt": "Létrehozva", "actions": "Műveletek"}, "status": {"pending_merchant": "Kereskedő jóváhagyására vár", "pending_customer": "Ügyfél jóváhagyására vár", "merchant_rejected": "Kereskedő elutasította", "customer_rejected": "Ügy<PERSON><PERSON><PERSON>", "accepted": "Elfogadva"}, "actions": {"sendQuote": "<PERSON><PERSON><PERSON><PERSON>", "rejectQuote": "<PERSON><PERSON><PERSON><PERSON>", "viewOrder": "Megrendelés megtekintése"}, "details": {"header": "<PERSON><PERSON><PERSON><PERSON>", "quoteSummary": "<PERSON><PERSON><PERSON><PERSON> összefoglalója", "customer": "Ügyfél", "items": "Tételek", "total": "Összesen", "subtotal": "Részösszeg", "shipping": "Szállítás", "tax": "<PERSON><PERSON>", "messages": "Üzenetek", "originalTotal": "Eredeti összeg", "quoteTotal": "<PERSON><PERSON><PERSON>lat összege", "discounts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pickQuoteItem": "Ajánlat tétel kiválasztása", "selectQuoteItem": "Válasszon ajánlat tételt üzenet íráshoz", "selectItem": "Tétel kiválasztása", "send": "<PERSON><PERSON><PERSON><PERSON>", "manage": "Kezelés", "manageQuote": "<PERSON><PERSON><PERSON><PERSON>", "updatePrice": "<PERSON><PERSON> <PERSON>", "overridePriceHint": "A termék egységárának felülírása", "phone": "Telefon", "spendingLimit": "Költési limit", "company": "<PERSON><PERSON><PERSON><PERSON>", "name": "Név"}, "confirmations": {"sendTitle": "<PERSON><PERSON><PERSON><PERSON>?", "sendDescription": "<PERSON><PERSON>, hogy elküldje ezt az ajánlatot az ügyfélnek. Szeretne folytatni?", "rejectTitle": "<PERSON><PERSON><PERSON><PERSON> elutasítás<PERSON>?", "rejectDescription": "<PERSON><PERSON>, hogy elutasítsa ennek az ügyfélnek az ajánlatát. Szeretne folytatni?"}, "toasts": {"sendSuccess": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>esen elküldve az ügyfélnek", "rejectSuccess": "Ügyfél a<PERSON> si<PERSON>n elutasítva", "messageSuccess": "Üzenet sikeresen elküldve az ügyfélnek", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>n f<PERSON>ít<PERSON>"}, "acceptance": {"message": "Az ügyfél elfogadta az ajánlatot. A megrendelés feldolgozásra kész."}}, "companies": {"title": "<PERSON><PERSON><PERSON>lat<PERSON>", "subtitle": "Ügyfél vállalatok és szervezetek kezelése", "createCompany": "Létrehozás", "noCompanies": "<PERSON><PERSON>hatók vállalatok", "noCompaniesDescription": "<PERSON><PERSON><PERSON> nincsenek vállalatok. Hozzon létre egyet a kezdéshez.", "table": {"name": "Név", "phone": "Telefon", "email": "E-mail", "address": "Cím", "employees": "Alkalmazottak", "customerGroup": "Ügyfélcsoport", "actions": "Műveletek"}, "form": {"generalInfo": "Általános inform<PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON> neve", "email": "E-mail", "phone": "Telefonszám", "address": "Cím", "city": "<PERSON><PERSON><PERSON>", "state": "Állam/Megye", "zipCode": "Irányí<PERSON>", "country": "<PERSON><PERSON><PERSON><PERSON>", "currency": "Pénznem", "logoUrl": "Vállalati logó URL", "selectCountry": "Ország kiválasztása", "selectCurrency": "Pénznem kiválasztása", "logo": "Vállalat<PERSON> logó", "website": "Weboldal", "description": "Le<PERSON><PERSON><PERSON>"}, "actions": {"edit": "V<PERSON><PERSON>lat szerkesztése", "delete": "Vállalat törlése", "viewEmployees": "Alkalmazottak megtekintése", "assignGroup": "Ügyfélcsoport hozzárendelése", "editDetails": "Részletek szerkesztése", "manageCustomerGroup": "Ügyfélcsoport kezelése", "approvalSettings": "Jóváhagyási beállítások"}, "details": {"overview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "employees": "Alkalmazottak", "orders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "Beállítások"}, "toasts": {"updateSuccess": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>esen frissítve", "updateError": "<PERSON><PERSON><PERSON><PERSON> fris<PERSON><PERSON><PERSON><PERSON>", "deleteSuccess": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>esen tö<PERSON>", "addToGroupSuccess": "Vállalat sikeresen hozzáadva az ügyfélcsoporthoz", "addToGroupError": "Vállalat ügyfélcsoporthoz adása si<PERSON>en", "removeFromGroupSuccess": "Vállalat si<PERSON>esen eltávolítva az ügyfélcsoportból", "removeFromGroupError": "Vállalat ügyfélcsoportból való eltávolítása sikertelen"}, "customerGroup": {"title": "Ügyfélcsoport kezelése", "addToGroup": "Hozzáadás ügyfélcsoporthoz", "hint": "{companyName} ügyfélcsoporthoz adása automatikusan hozzáadja a {employeeCount} kapcsolódó alkalmazottat az ügyfélcsoporthoz.", "customerGroupColumn": "Ügyfélcsoport", "actionsColumn": "Műveletek", "addButton": "Hozzáadás", "removeButton": "Eltávolítás", "noGroupsFound": "<PERSON>em találhatók ügyfélcsoportok"}, "approvalSettings": {"title": "Vállalati jóváhagyási beállítások", "requiresAdminApproval": "Adminisztrátori jóváhagyást igényel", "requiresAdminApprovalDesc": "Vállalati adminisztrátori jóváhagyás szükséges a vállalat összes megrendeléséhez.", "requiresSalesManagerApproval": "Értékesítési vezető jóváhagyását igényel", "requiresSalesManagerApprovalDesc": "Értékesítési vezető jóváhagyása szükséges a vállalat összes megrendeléséhez.", "updateSuccess": "Vállalati jóváhagyási beállítások sikeresen frissítve", "updateError": "Vállalati jóváhagyási beállítások frissítése si<PERSON>telen"}, "companyDetails": {"notFound": "V<PERSON><PERSON>lat nem található", "phone": "Telefon", "email": "E-mail", "address": "Cím", "city": "<PERSON><PERSON><PERSON>", "state": "Állam/Megye", "currency": "Pénznem", "customerGroup": "Ügyfélcsoport", "approvalSettings": "Jóváhagyási beállítások", "requiresAdminApproval": "Adminisztrátori jóváhagyást igényel", "requiresSalesManagerApproval": "Értékesítési vezető jóváhagyását igényel", "noApprovalRequired": "<PERSON><PERSON><PERSON> s<PERSON>ükség jóváhagyásra", "employees": "Alkalmazottak", "name": "Név", "spendingLimit": "Költési limit", "actions": "Műveletek", "admin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "add": "Hozzáadás", "noEmployees": "<PERSON><PERSON><PERSON> rekord", "noEmployeesDesc": "Ennek a vállalatnak nincsenek alkalmazottai."}, "employees": {"createEmployee": "Vállalati ügyfél hozzáadása", "createEmployeeSuccess": "Alkalmazott sikeresen létrehozva", "createCustomerError": "Ügyfél létreho<PERSON>", "createEmployeeError": "Alkalmazott létrehozása sikertelen", "form": {"details": "Részletek", "firstName": "Keresztnév", "lastName": "Vezetéknév", "email": "E-mail", "phone": "Telefon", "permissions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spendingLimit": "Költési limit", "adminAccess": "Adminisztr<PERSON><PERSON>", "isAdmin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isAdminDesc": "Engedélyezés adminisztrá<PERSON> ho<PERSON><PERSON><PERSON><PERSON><PERSON>", "isAdminTooltip": "Az adminisztrátorok kezelhetik a vállalat részleteit és az alkalmazottak engedélyeit.", "firstNamePlaceholder": "<PERSON><PERSON><PERSON>", "lastNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "emailPlaceholder": "<EMAIL>", "phonePlaceholder": "+36 30 123 4567", "spendingLimitPlaceholder": "10000", "saving": "Mentés...", "save": "Men<PERSON>s"}, "actions": {"edit": "Szerkesztés", "delete": "Törlés", "deleteSuccess": "Alkalmazott sikeresen törölve"}, "edit": {"title": "Alkalmazott szerkesztése", "updateSuccess": "Alkalmazott sikeresen frissítve", "editCustomerDetails": "Ügyfél részleteinek szerkesztése", "company": "<PERSON><PERSON><PERSON><PERSON>"}}}, "approvals": {"title": "Jóváhagyások", "subtitle": "Megrendelés jóváhagyási munkafolyamatok kezelése", "noApprovals": "<PERSON><PERSON><PERSON> rekord", "noApprovalsDescription": "<PERSON><PERSON><PERSON> nincsenek jóváhagyási kérések.", "table": {"orderId": "Megrendelés ID", "customer": "Ügyfél", "company": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Összeg", "status": "<PERSON><PERSON><PERSON>", "requestedAt": "<PERSON><PERSON><PERSON><PERSON>", "actions": "Műveletek"}, "status": {"pending": "Függőben", "approved": "Jóváhagyva", "rejected": "Elutasítva", "expired": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"approve": "Jóváhagyás", "reject": "Elutasítás", "review": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "details": {"approvalRequest": "Jóváhagyási kérés", "requestedBy": "Kérelmező", "reason": "Ok", "orderDetails": "Megrendelés r<PERSON>i"}, "confirmations": {"approveTitle": "Megrendelés jóváhagyása?", "approveDescription": "<PERSON><PERSON>, hogy jóváhagyja ezt a megrendelést. Szeretne folytatni?", "rejectTitle": "Megrendelés elutasítása?", "rejectDescription": "<PERSON><PERSON>, hogy elutasítja ezt a megrendelés jóváhagyási kérést. Szeretne folytatni?"}, "toasts": {"approveSuccess": "Megrendelés sikeresen jóváhagyva", "rejectSuccess": "Megrendelés jóváhagyása elutasítva"}}}, "common": {"loading": "Betöltés...", "noData": "Nincs adat", "search": "Keresés", "filter": "Szűrő", "export": "Exportálás", "import": "Importálás", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "save": "Men<PERSON>s", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Törlés", "edit": "Szerkesztés", "view": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "create": "Létrehozás", "back": "<PERSON><PERSON><PERSON>", "next": "Következő oldal", "previous": "Előző oldal", "error": "Hiba", "confirmDeletion": "Törlés megerősítése", "confirmDeletionDesc": "Biztos, hogy törölni szeretné ezt az elemet? Ez a művelet nem von<PERSON>ó v<PERSON>za.", "confirmLeaveForm": "Biztos, hogy el szeretné hagyni ezt az űrlapot?", "confirmLeaveFormDesc": "<PERSON><PERSON> men<PERSON> m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ha elhagyja <PERSON> ű<PERSON>.", "continue": "Folytatás"}}}